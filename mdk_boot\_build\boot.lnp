--cpu=Cortex-M4.fp.sp
".\_build\dfu_public_key.o"
".\_build\main.o"
".\_build\boards.o"
".\_build\ble_srv_common.o"
".\_build\nrf_bootloader.o"
".\_build\nrf_bootloader_app_start.o"
".\_build\nrf_bootloader_app_start_final.o"
".\_build\nrf_bootloader_dfu_timers.o"
".\_build\nrf_bootloader_fw_activation.o"
".\_build\nrf_bootloader_info.o"
".\_build\nrf_bootloader_wdt.o"
".\_build\nrf_crypto_ecc.o"
".\_build\nrf_crypto_ecdsa.o"
".\_build\nrf_crypto_hash.o"
".\_build\nrf_crypto_init.o"
".\_build\nrf_crypto_shared.o"
".\_build\oberon_backend_chacha_poly_aead.o"
".\_build\oberon_backend_ecc.o"
".\_build\oberon_backend_ecdh.o"
".\_build\oberon_backend_ecdsa.o"
".\_build\oberon_backend_eddsa.o"
".\_build\oberon_backend_hash.o"
".\_build\oberon_backend_hmac.o"
".\_build\nrf_sw_backend_hash.o"
".\_build\micro_ecc_backend_ecc.o"
".\_build\micro_ecc_backend_ecdh.o"
".\_build\micro_ecc_backend_ecdsa.o"
".\_build\dfu-cc.pb.o"
".\_build\nrf_dfu.o"
".\_build\nrf_dfu_ble.o"
".\_build\nrf_dfu_flash.o"
".\_build\nrf_dfu_handling_error.o"
".\_build\nrf_dfu_mbr.o"
".\_build\nrf_dfu_req_handler.o"
".\_build\nrf_dfu_settings.o"
".\_build\nrf_dfu_settings_svci.o"
".\_build\nrf_dfu_transport.o"
".\_build\nrf_dfu_utils.o"
".\_build\nrf_dfu_validation.o"
".\_build\nrf_dfu_ver_validation.o"
".\_build\nrf_nvmc.o"
".\_build\nrfx_atomic.o"
".\_build\app_error_weak.o"
".\_build\app_scheduler.o"
".\_build\app_util_platform.o"
".\_build\crc32.o"
".\_build\mem_manager.o"
".\_build\nrf_assert.o"
".\_build\nrf_atfifo.o"
".\_build\nrf_atomic.o"
".\_build\nrf_balloc.o"
".\_build\nrf_fprintf.o"
".\_build\nrf_fprintf_format.o"
".\_build\nrf_fstorage.o"
".\_build\nrf_fstorage_nvmc.o"
".\_build\nrf_fstorage_sd.o"
".\_build\nrf_memobj.o"
".\_build\nrf_queue.o"
".\_build\nrf_ringbuf.o"
".\_build\nrf_section_iter.o"
".\_build\nrf_strerror.o"
".\_build\sha256.o"
".\_build\nrf_log_frontend.o"
".\_build\nrf_log_str_formatter.o"
"..\nRF5_SDK_17.0.2_d674dde\external\nrf_oberon\lib\cortex-m4\hard-float\short-wchar\oberon_3.0.6.lib"
".\_build\nrf_dfu_svci.o"
".\_build\nrf_dfu_svci_handler.o"
".\_build\nrf_svc_handler.o"
".\_build\nrf_sdh.o"
".\_build\nrf_sdh_ble.o"
".\_build\nrf_sdh_soc.o"
"..\source\lib\micro_ecc_lib_nrf52.lib"
".\_build\pb_common.o"
".\_build\pb_decode.o"
".\_build\arm_startup_nrf52.o"
".\_build\system_nrf52.o"
--library_type=microlib --strict --scatter ".\_build\boot.sct"
--diag_suppress 6330 --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\_build\boot.map" -o .\_build\boot.axf