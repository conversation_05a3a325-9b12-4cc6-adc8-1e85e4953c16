1、SDK版本
芯片：NRF52832-QFAA-R
使用SDK：nRF5_SDK_17.0.2_d674dde
使用到库包：
ARM.CMSIS.5.6.0
NordicSemiconductor.nRF_DeviceFamilyPack.8.35.0

2、设置RTT调试
在sdk_config.h文件中更改NRF_LOG_BACKEND_RTT_ENABLED和NRF_LOG_BACKEND_UART_ENABLED宏

3、CONFIG_GPIO_AS_PINRESET
设置该宏之后要全片擦出才能生效

4、扫描设置
可以多次使用nrf_ble_scan_filter_set设置过滤信息，逻辑或
要修改sdk_config.h中的NRF_BLE_SCAN_NAME_CNT定义

5、

6、中文字符串乱码问题
1)使用window自带记事本或者NotePad++打开文件，另存为ANSI格式。Keil重新打开
2)Keil中选择GB2312简体
