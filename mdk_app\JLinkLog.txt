T10FC 280476:418.879   SEGGER J-Link V7.94i Log File
T10FC 280476:418.946   DLL Compiled: Feb  7 2024 17:04:01
T10FC 280476:418.962   Logging started @ 2025-06-09 07:31
T10FC 280476:418.975   Process: D:\Keil_mdk\UV4\UV4.exe
T10FC 280476:418.989 - 20086.011ms
T10FC 280476:419.009 JLINK_SetWarnOut<PERSON>andler(...)
T10FC 280476:419.022 - 0.013ms
T10FC 280476:419.036 JLINK_OpenEx(...)
T10FC 280476:674.639   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T10FC 280476:677.031   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T10FC 280476:677.504   Decompressing FW timestamp took 379 us
T10FC 280476:688.601   Hardware: V9.40
T10FC 280476:688.682   S/N: 69409381
T10FC 280476:688.764   OEM: SEG<PERSON>R
T10FC 280476:688.837   Feature(s): RDI, GD<PERSON>, FlashDL, FlashB<PERSON>, JFlash
T10FC 280476:690.653   Bootloader: (Could not read)
T10FC 280476:692.765   TELNET listener socket opened on port 19021
T10FC 280476:706.484   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T10FC 280476:706.639   WEBSRV Failed to put socket into listener state (port 19080)
T10FC 280476:706.677   WEBSRV Failed to put socket into listener state (port 19081)
T10FC 280476:706.857   WEBSRV Webserver running on local port 19082
T10FC 280476:707.032   Looking for J-Link GUI Server exe at: D:\Keil_mdk\ARM\Segger\JLinkGUIServer.exe
T10FC 280476:707.076   Looking for J-Link GUI Server exe at: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T10FC 280476:707.104   Forking J-Link GUI Server: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T10FC 280476:741.846   J-Link GUI Server info: "J-Link GUI server V7.94i "
T10FC 280476:754.680 - 335.642ms returns "O.K."
T10FC 280476:754.746 JLINK_GetEmuCaps()
T10FC 280476:754.763 - 0.017ms returns 0xB9FF7BBF
T10FC 280476:754.783 JLINK_TIF_GetAvailable(...)
T10FC 280476:755.230 - 0.446ms
T10FC 280476:756.122 JLINK_SetErrorOutHandler(...)
T10FC 280476:756.202 - 0.080ms
T10FC 280476:756.266 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\project\FWPVT1\FWPVT\mdk_app\JLinkSettings.ini"", ...). 
T10FC 280476:762.015 - 5.747ms returns 0x00
T10FC 280476:762.117 JLINK_ExecCommand("Device = nRF52832_xxAA", ...). 
T10FC 280476:762.652   Device "NRF52832_XXAA" selected.
T10FC 280476:763.455 - 1.288ms returns 0x00
T10FC 280476:763.514 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T10FC 280476:763.563 - 0.003ms returns 0x01
T10FC 280476:763.619 JLINK_GetHardwareVersion()
T10FC 280476:763.660 - 0.040ms returns 94000
T10FC 280476:763.700 JLINK_GetDLLVersion()
T10FC 280476:763.738 - 0.037ms returns 79409
T10FC 280476:763.781 JLINK_GetOEMString(...)
T10FC 280476:763.823 JLINK_GetFirmwareString(...)
T10FC 280476:763.862 - 0.038ms
T10FC 280476:763.909 JLINK_GetDLLVersion()
T10FC 280476:763.948 - 0.038ms returns 79409
T10FC 280476:763.988 JLINK_GetCompileDateTime()
T10FC 280476:764.025 - 0.036ms
T10FC 280476:764.070 JLINK_GetFirmwareString(...)
T10FC 280476:764.122 - 0.052ms
T10FC 280476:764.165 JLINK_GetHardwareVersion()
T10FC 280476:764.205 - 0.039ms returns 94000
T10FC 280476:764.247 JLINK_GetSN()
T10FC 280476:764.287 - 0.039ms returns 69409381
T10FC 280476:764.328 JLINK_GetOEMString(...)
T10FC 280476:764.376 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T10FC 280476:766.270 - 1.893ms returns 0x00
T10FC 280476:766.331 JLINK_HasError()
T10FC 280476:766.376 JLINK_SetSpeed(1000)
T10FC 280476:766.831 - 0.453ms
T10FC 280476:767.537 JLINK_HasError()
T10FC 280476:767.616 JLINK_SetResetType(JLINKARM_RESET_TYPE_NORMAL)
T10FC 280476:767.661 - 0.045ms returns JLINKARM_RESET_TYPE_NORMAL
T10FC 280476:767.706 JLINK_Reset()
T10FC 280476:768.219   InitTarget() start
T10FC 280476:768.283    J-Link Script File: Executing InitTarget()
T10FC 280476:773.660   InitTarget() end - Took 5.36ms
T10FC 280476:775.231   Found SW-DP with ID 0x2BA01477
T10FC 280476:779.848   DPIDR: 0x2BA01477
T10FC 280476:779.893   CoreSight SoC-400 or earlier
T10FC 280476:779.931   Scanning AP map to find all available APs
T10FC 280476:782.104   AP[2]: Stopped AP scan as end of AP map has been reached
T10FC 280476:782.145   AP[0]: AHB-AP (IDR: 0x24770011)
T10FC 280476:782.212   AP[1]: JTAG-AP (IDR: 0x02880000)
T10FC 280476:782.235   Iterating through AP map to find AHB-AP to use
T10FC 280476:784.351   AP[0]: Core found
T10FC 280476:784.395   AP[0]: AHB-AP ROM base: 0xE00FF000
T10FC 280476:785.552   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T10FC 280476:785.599   Found Cortex-M4 r0p1, Little endian.
T10FC 280476:786.563   -- Max. mem block: 0x00010C40
T10FC 280476:787.547   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T10FC 280476:788.583   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T10FC 280476:789.653   CPU_ReadMem(4 bytes @ 0xE0002000)
T10FC 280476:790.757   FPUnit: 6 code (BP) slots and 2 literal slots
T10FC 280476:790.816   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T10FC 280476:791.813   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T10FC 280476:792.924   CPU_ReadMem(4 bytes @ 0xE0001000)
T10FC 280476:793.944   CPU_WriteMem(4 bytes @ 0xE0001000)
T10FC 280476:795.040   CPU_ReadMem(4 bytes @ 0xE000ED88)
T10FC 280476:796.106   CPU_WriteMem(4 bytes @ 0xE000ED88)
T10FC 280476:797.097   CPU_ReadMem(4 bytes @ 0xE000ED88)
T10FC 280476:798.151   CPU_WriteMem(4 bytes @ 0xE000ED88)
T10FC 280476:799.234   CoreSight components:
T10FC 280476:799.307   ROMTbl[0] @ E00FF000
T10FC 280476:799.373   CPU_ReadMem(64 bytes @ 0xE00FF000)
T10FC 280476:801.535   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T10FC 280476:803.071   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T10FC 280476:803.145   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T10FC 280476:804.653   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T10FC 280476:804.738   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T10FC 280476:806.392   [0][2]: E0002000 CID B105E00D PID 002BB003 FPB
T10FC 280476:806.474   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T10FC 280476:808.032   [0][3]: E0000000 CID B105E00D PID 003BB001 ITM
T10FC 280476:808.106   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T10FC 280476:809.670   [0][4]: E0040000 CID B105900D PID 000BB9A1 TPIU
T10FC 280476:809.736   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T10FC 280476:811.301   [0][5]: E0041000 CID B105900D PID 000BB925 ETM
T10FC 280476:811.381   CPU is running
T10FC 280476:811.435   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T10FC 280476:812.413   CPU is running
T10FC 280476:812.467   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T10FC 280476:813.488   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T10FC 280476:814.672   Reset: Reset device via AIRCR.SYSRESETREQ.
T10FC 280476:814.745   CPU is running
T10FC 280476:814.808   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T10FC 280476:871.062   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T10FC 280476:872.224   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T10FC 280476:882.272   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T10FC 280476:889.609   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T10FC 280476:899.598   CPU_WriteMem(4 bytes @ 0xE0002000)
T10FC 280476:900.683   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T10FC 280476:901.730   CPU_ReadMem(4 bytes @ 0xE0001000)
T10FC 280476:902.798 - 135.091ms
T10FC 280476:902.880 JLINK_GetId()
T10FC 280476:903.557 - 0.675ms returns 0x2BA01477
T10FC 280476:911.179 JLINK_GetFirmwareString(...)
T10FC 280476:911.274 - 0.095ms
T10FC 280478:292.063 JLINK_Close()
T10FC 280478:292.607   CPU_ReadMem(4 bytes @ 0xE0001000)
T10FC 280478:293.609   CPU_WriteMem(4 bytes @ 0xE0001004)
T10FC 280478:306.990 - 14.925ms
T10FC 280478:307.058   
T10FC 280478:307.077   Closed
