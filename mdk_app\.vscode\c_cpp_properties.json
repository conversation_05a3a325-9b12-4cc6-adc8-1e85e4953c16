{"configurations": [{"name": "YSJ-20", "includePath": ["e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_advertising", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_dtm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_link_ctx_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_racp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ancs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ans_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_bas", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_bas_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_cscs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_cts_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_dis", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_gls", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hids", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hrs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hrs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hts", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ias", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ias_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lbs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lbs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lls", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_nus", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_nus_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_rscs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_rscs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_tps", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\nrf_ble_gatt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\nrf_ble_qwr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\peer_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\boards", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic_fifo", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic_flags", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\balloc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader\\ble_dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader\\dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bsp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\button", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\cli", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crc16", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crc32", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crypto", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\csense", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\csense_drv", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\delay", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\ecc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\experimental_section_vars", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\experimental_task_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fds", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fifo", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fstorage", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\gfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\gpiote", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\hardfault", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\hci", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\led_softblink", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\log", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\log\\src", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\low_power_pwm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mem_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\memobj", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mpu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mutex", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\pwm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\pwr_mgmt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\queue", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\ringbuf", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\scheduler", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\sdcard", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\slip", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\sortlist", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\spi_mngr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\stack_guard", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\strerror", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\svc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\timer", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\twi_mngr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\twi_sensor", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\uart", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\audio", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\cdc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\cdc\\acm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\generic", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\kbd", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\mouse", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\msc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\util", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\ac_rec_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\ble_oob_advdata_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\le_oob_rec_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ac_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_oob_advdata", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_pair_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_pair_msg", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ep_oob_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\hs_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\le_oob_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\generic\\message", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\generic\\record", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\launchapp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\parser\\message", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\parser\\record", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\text", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\uri", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\platform", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t2t_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t2t_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\apdu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\cc_file", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\hl_detection_procedure", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\tlv", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\s132\\headers", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\s132\\headers\\nrf52", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\fprintf", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\segger_rtt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\utf_converter", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\integration\\nrfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\integration\\nrfx\\legacy", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\include", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\hal", "e:\\FWPVT\\source\\app", "e:\\FWPVT\\source\\sm", "e:\\FWPVT\\source\\driver", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\drivers_nrf\\twi_master\\deprecated", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\soc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\src", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\src\\prs", "e:\\FWPVT\\doc"], "defines": ["APP_TIMER_V2", "APP_TIMER_V2_RTC1_ENABLED", "CONFIG_NFCT_PINS_AS_GPIOS", "FLOAT_ABI_HARD", "NRF52", "NRF52832_XXAA", "NRF52_PAN_74", "NRF_SD_BLE_API_VERSION=7", "S132", "SOFTDEVICE_PRESENT", "__HEAP_SIZE=8192", "__STACK_SIZE=8192", "NRF_DFU_SVCI_ENABLED", "CONFIG_GPIO_AS_PINRESET", "NRF_DFU_TRANSPORT_BLE=1", "BL_SETTINGS_ACCESS_ONLY", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "YSJ-20V121", "includePath": ["e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_advertising", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_dtm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_link_ctx_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_racp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ancs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ans_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_bas", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_bas_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_cscs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_cts_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_dis", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_gls", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hids", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hrs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hrs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_hts", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ias", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_ias_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lbs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lbs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_lls", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_nus", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_nus_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_rscs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_rscs_c", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\ble_services\\ble_tps", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\nrf_ble_gatt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\nrf_ble_qwr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\ble\\peer_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\boards", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic_fifo", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\atomic_flags", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\balloc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader\\ble_dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bootloader\\dfu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\bsp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\button", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\cli", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crc16", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crc32", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\crypto", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\csense", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\csense_drv", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\delay", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\ecc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\experimental_section_vars", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\experimental_task_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fds", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fifo", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\fstorage", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\gfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\gpiote", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\hardfault", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\hci", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\led_softblink", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\log", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\log\\src", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\low_power_pwm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mem_manager", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\memobj", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mpu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\mutex", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\pwm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\pwr_mgmt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\queue", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\ringbuf", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\scheduler", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\sdcard", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\slip", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\sortlist", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\spi_mngr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\stack_guard", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\strerror", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\svc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\timer", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\twi_mngr", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\twi_sensor", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\uart", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\audio", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\cdc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\cdc\\acm", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\generic", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\kbd", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\hid\\mouse", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\usbd\\class\\msc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\libraries\\util", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\ac_rec_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\ble_oob_advdata_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\conn_hand_parser\\le_oob_rec_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ac_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_oob_advdata", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_pair_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ble_pair_msg", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\ep_oob_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\hs_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\connection_handover\\le_oob_rec", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\generic\\message", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\generic\\record", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\launchapp", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\parser\\message", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\parser\\record", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\text", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\ndef\\uri", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\platform", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t2t_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t2t_parser", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_lib", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\apdu", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\cc_file", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\hl_detection_procedure", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\nfc\\t4t_parser\\tlv", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\common", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\s132\\headers", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\softdevice\\s132\\headers\\nrf52", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\fprintf", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\segger_rtt", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\external\\utf_converter", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\integration\\nrfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\integration\\nrfx\\legacy", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\include", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\hal", "e:\\FWPVT\\source\\app", "e:\\FWPVT\\source\\sm", "e:\\FWPVT\\source\\driver", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\soc", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\src", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\modules\\nrfx\\drivers\\src\\prs", "e:\\FWPVT\\nRF5_SDK_17.0.2_d674dde\\components\\drivers_nrf\\twi_master\\deprecated", "e:\\FWPVT\\doc"], "defines": ["APP_TIMER_V2", "APP_TIMER_V2_RTC1_ENABLED", "CONFIG_GPIO_AS_PINRESET", "CONFIG_NFCT_PINS_AS_GPIOS", "FLOAT_ABI_HARD", "NRF52", "NRF52832_XXAA", "NRF52_PAN_74", "NRF_SD_BLE_API_VERSION=7", "S132", "SOFTDEVICE_PRESENT", "__HEAP_SIZE=8192", "__STACK_SIZE=8192", "NRF_DFU_SVCI_ENABLED", "NRF_DFU_TRANSPORT_BLE=1", "BL_SETTINGS_ACCESS_ONLY", "DEBUG", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}