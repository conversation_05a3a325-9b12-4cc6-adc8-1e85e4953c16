.\_build\spiflash20.o: ..\source\driver\spiflash20.c
.\_build\spiflash20.o: ..\source\app\main.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\string.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\nordic_common.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf52.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\system_nrf52.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf52_bitfields.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf51_to_nrf52.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf52_name_change.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\compiler_abstraction.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_svc.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_error.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_err.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_gap.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_hci.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_ranges.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_types.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_l2cap.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_gatt.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_gattc.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\ble_gatts.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\hal\nrf_gpio.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\nrfx.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\integration\nrfx\nrfx_config.h
.\_build\spiflash20.o: ..\source\app\sdk_config.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\drivers/nrfx_common.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf_peripherals.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.44.1\Device\Include\nrf52832_peripherals.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\integration\nrfx\nrfx_glue.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\integration\nrfx\legacy/apply_old_config.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\soc/nrfx_irqs.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\soc/nrfx_irqs_nrf52832.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\nrf_assert.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\app_util.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf52\nrf_mbr.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\app_util_platform.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_soc.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_error_soc.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_nvic.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\app_error.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\sdk_errors.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\app_error_weak.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\soc/nrfx_coredep.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\soc/nrfx_atomic.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\nrfx.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\sdk_resources.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\softdevice\s132\headers\nrf_sd_def.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\drivers/nrfx_errors.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\delay\nrf_delay.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\timer\app_timer.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log_instance.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\experimental_section_vars\nrf_section.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log_types.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\sortlist\nrf_sortlist.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\fifo\app_fifo.h
.\_build\spiflash20.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdlib.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\sdk_common.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\sdk_os.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\util\sdk_macros.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\strerror\nrf_strerror.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\src\nrf_log_internal.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log_ctrl.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log_backend_interface.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\memobj\nrf_memobj.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\balloc\nrf_balloc.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\components\libraries\log\nrf_log_default_backends.h
.\_build\spiflash20.o: ..\source\driver\spiflash20.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\integration\nrfx\legacy\nrf_drv_spi.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\drivers\include\nrfx_spim.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\hal/nrf_spim.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\drivers\include\nrfx_spi.h
.\_build\spiflash20.o: ..\nRF5_SDK_17.0.2_d674dde\modules\nrfx\hal/nrf_spi.h
