--cpu=Cortex-M4.fp
".\_build\main.o"
".\_build\algo.o"
".\_build\pages.o"
".\_build\protocol.o"
".\_build\utils.o"
".\_build\factory.o"
".\_build\sm.o"
".\_build\sm_power_on.o"
".\_build\sm_power_off.o"
".\_build\sm_ready.o"
".\_build\sm_measure.o"
".\_build\sm_result.o"
".\_build\sm_history.o"
".\_build\sm_calibrate.o"
".\_build\sm_error.o"
".\_build\sm_uncali.o"
".\_build\sm_locked.o"
".\_build\ble_conn.o"
".\_build\analog.o"
".\_build\frame.o"
".\_build\power.o"
".\_build\storage.o"
".\_build\led_array.o"
".\_build\pwm.o"
".\_build\key.o"
".\_build\led_state.o"
".\_build\saadc.o"
".\_build\light.o"
".\_build\motor.o"
".\_build\kalman_filter.o"
".\_build\hide_trig.o"
".\_build\watchdog.o"
".\_build\rng.o"
".\_build\bm8563.o"
".\_build\spiflash20.o"
".\_build\tm1640.o"
".\_build\ble_advdata.o"
".\_build\ble_advertising.o"
".\_build\ble_conn_params.o"
".\_build\ble_conn_state.o"
".\_build\ble_link_ctx_manager.o"
".\_build\ble_srv_common.o"
".\_build\nrf_ble_gatt.o"
".\_build\nrf_ble_qwr.o"
".\_build\ble_nus.o"
".\_build\ble_dis.o"
".\_build\ble_bas.o"
".\_build\nrf_drv_clock.o"
".\_build\nrf_drv_uart.o"
".\_build\nrfx_atomic.o"
".\_build\nrfx_clock.o"
".\_build\nrfx_gpiote.o"
".\_build\nrfx_prs.o"
".\_build\nrfx_uart.o"
".\_build\nrfx_uarte.o"
".\_build\nrfx_pwm.o"
".\_build\nrfx_timer.o"
".\_build\nrfx_saadc.o"
".\_build\nrfx_wdt.o"
".\_build\nrfx_ppi.o"
".\_build\nrf_drv_ppi.o"
".\_build\twi_hw_master.o"
".\_build\nrf_drv_spi.o"
".\_build\nrfx_spi.o"
".\_build\nrfx_temp.o"
".\_build\nrfx_lpcomp.o"
".\_build\utf.o"
".\_build\app_button.o"
".\_build\app_error.o"
".\_build\app_error_handler_keil.o"
".\_build\app_error_weak.o"
".\_build\app_fifo.o"
".\_build\app_scheduler.o"
".\_build\app_timer2.o"
".\_build\app_uart_fifo.o"
".\_build\app_util_platform.o"
".\_build\drv_rtc.o"
".\_build\hardfault_implementation.o"
".\_build\nrf_assert.o"
".\_build\nrf_atfifo.o"
".\_build\nrf_atflags.o"
".\_build\nrf_atomic.o"
".\_build\nrf_balloc.o"
".\_build\nrf_fprintf.o"
".\_build\nrf_fprintf_format.o"
".\_build\nrf_memobj.o"
".\_build\nrf_pwr_mgmt.o"
".\_build\nrf_ringbuf.o"
".\_build\nrf_section_iter.o"
".\_build\nrf_sortlist.o"
".\_build\nrf_strerror.o"
".\_build\retarget.o"
".\_build\fds.o"
".\_build\nrf_fstorage.o"
".\_build\nrf_fstorage_sd.o"
".\_build\nrf_queue.o"
".\_build\app_pwm.o"
".\_build\ble_dfu.o"
".\_build\ble_dfu_bonded.o"
".\_build\ble_dfu_unbonded.o"
".\_build\nrf_dfu_svci.o"
".\_build\nrf_log_backend_rtt.o"
".\_build\nrf_log_backend_serial.o"
".\_build\nrf_log_default_backends.o"
".\_build\nrf_log_frontend.o"
".\_build\nrf_log_str_formatter.o"
".\_build\segger_rtt.o"
".\_build\segger_rtt_syscalls_keil.o"
".\_build\segger_rtt_printf.o"
".\_build\nrf_sdh.o"
".\_build\nrf_sdh_ble.o"
".\_build\nrf_sdh_soc.o"
".\_build\arm_startup_nrf52.o"
".\_build\system_nrf52.o"
--library_type=microlib --strict --scatter ".\_build\app.sct"
--diag_suppress 6330 --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\_build\app.map" -o .\_build\app.elf