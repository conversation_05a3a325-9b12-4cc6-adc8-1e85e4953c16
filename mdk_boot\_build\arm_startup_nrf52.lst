


ARM Macro Assembler    Page 1 


    1 00000000         ; Copyright (c) 2009-2021 ARM Limited. All rights reserv
                       ed.
    2 00000000         ; 
    3 00000000         ;     SPDX-License-Identifier: Apache-2.0
    4 00000000         ; 
    5 00000000         ; Licensed under the Apache License, Version 2.0 (the Li
                       cense); you may
    6 00000000         ; not use this file except in compliance with the Licens
                       e.
    7 00000000         ; You may obtain a copy of the License at
    8 00000000         ; 
    9 00000000         ;     www.apache.org/licenses/LICENSE-2.0
   10 00000000         ; 
   11 00000000         ; Unless required by applicable law or agreed to in writ
                       ing, software
   12 00000000         ; distributed under the License is distributed on an AS 
                       IS BASIS, WITHOUT
   13 00000000         ; WARRANTIES OR CONDITIONS OF ANY KIND, either express o
                       r implied.
   14 00000000         ; See the License for the specific language governing pe
                       rmissions and
   15 00000000         ; limitations under the License.
   16 00000000         ; 
   17 00000000         ; NOTICE: This file has been modified by Nordic Semicond
                       uctor ASA.
   18 00000000         
   19 00000000                 IF               :DEF: __STARTUP_CONFIG
   26                          ENDIF
   27 00000000         
   28 00000000                 IF               :DEF: __STARTUP_CONFIG
   33 00000000 00001000 
                       Stack_Size
                               EQU              4096
   34 00000000                 ENDIF
   35 00000000         
   36 00000000                 IF               :DEF: __STARTUP_CONFIG
   39 00000000 00000003 
                       Stack_Align
                               EQU              3
   40 00000000                 ENDIF
   41 00000000         
   42 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=Stack_Align
   43 00000000         Stack_Mem
                               SPACE            Stack_Size
   44 00001000         __initial_sp
   45 00001000         
   46 00001000                 IF               :DEF: __STARTUP_CONFIG
                               ELIF             :DEF: __HEAP_SIZE
   49 00001000 00000000 
                       Heap_Size
                               EQU              __HEAP_SIZE
   50 00001000                 ELSE
   52                          ENDIF
   53 00001000         
   54 00001000                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   55 00000000         __heap_base
   56 00000000         Heap_Mem



ARM Macro Assembler    Page 2 


                               SPACE            Heap_Size
   57 00000000         __heap_limit
   58 00000000         
   59 00000000                 PRESERVE8
   60 00000000                 THUMB
   61 00000000         
   62 00000000         ; Vector Table Mapped to Address 0 at Reset
   63 00000000         
   64 00000000                 AREA             RESET, DATA, READONLY
   65 00000000                 EXPORT           __Vectors
   66 00000000                 EXPORT           __Vectors_End
   67 00000000                 EXPORT           __Vectors_Size
   68 00000000         
   69 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   70 00000004 00000000        DCD              Reset_Handler
   71 00000008 00000000        DCD              NMI_Handler
   72 0000000C 00000000        DCD              HardFault_Handler
   73 00000010 00000000        DCD              MemoryManagement_Handler
   74 00000014 00000000        DCD              BusFault_Handler
   75 00000018 00000000        DCD              UsageFault_Handler
   76 0000001C 00000000        DCD              0           ; Reserved
   77 00000020 00000000        DCD              0           ; Reserved
   78 00000024 00000000        DCD              0           ; Reserved
   79 00000028 00000000        DCD              0           ; Reserved
   80 0000002C 00000000        DCD              SVC_Handler
   81 00000030 00000000        DCD              DebugMon_Handler
   82 00000034 00000000        DCD              0           ; Reserved
   83 00000038 00000000        DCD              PendSV_Handler
   84 0000003C 00000000        DCD              SysTick_Handler
   85 00000040         
   86 00000040         ; External Interrupts
   87 00000040 00000000        DCD              POWER_CLOCK_IRQHandler
   88 00000044 00000000        DCD              RADIO_IRQHandler
   89 00000048 00000000        DCD              UARTE0_UART0_IRQHandler
   90 0000004C 00000000        DCD              SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TW
I0_IRQHandler
   91 00000050 00000000        DCD              SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TW
I1_IRQHandler
   92 00000054 00000000        DCD              NFCT_IRQHandler
   93 00000058 00000000        DCD              GPIOTE_IRQHandler
   94 0000005C 00000000        DCD              SAADC_IRQHandler
   95 00000060 00000000        DCD              TIMER0_IRQHandler
   96 00000064 00000000        DCD              TIMER1_IRQHandler
   97 00000068 00000000        DCD              TIMER2_IRQHandler
   98 0000006C 00000000        DCD              RTC0_IRQHandler
   99 00000070 00000000        DCD              TEMP_IRQHandler
  100 00000074 00000000        DCD              RNG_IRQHandler
  101 00000078 00000000        DCD              ECB_IRQHandler
  102 0000007C 00000000        DCD              CCM_AAR_IRQHandler
  103 00000080 00000000        DCD              WDT_IRQHandler
  104 00000084 00000000        DCD              RTC1_IRQHandler
  105 00000088 00000000        DCD              QDEC_IRQHandler
  106 0000008C 00000000        DCD              COMP_LPCOMP_IRQHandler
  107 00000090 00000000        DCD              SWI0_EGU0_IRQHandler
  108 00000094 00000000        DCD              SWI1_EGU1_IRQHandler
  109 00000098 00000000        DCD              SWI2_EGU2_IRQHandler
  110 0000009C 00000000        DCD              SWI3_EGU3_IRQHandler



ARM Macro Assembler    Page 3 


  111 000000A0 00000000        DCD              SWI4_EGU4_IRQHandler
  112 000000A4 00000000        DCD              SWI5_EGU5_IRQHandler
  113 000000A8 00000000        DCD              TIMER3_IRQHandler
  114 000000AC 00000000        DCD              TIMER4_IRQHandler
  115 000000B0 00000000        DCD              PWM0_IRQHandler
  116 000000B4 00000000        DCD              PDM_IRQHandler
  117 000000B8 00000000        DCD              0           ; Reserved
  118 000000BC 00000000        DCD              0           ; Reserved
  119 000000C0 00000000        DCD              MWU_IRQHandler
  120 000000C4 00000000        DCD              PWM1_IRQHandler
  121 000000C8 00000000        DCD              PWM2_IRQHandler
  122 000000CC 00000000        DCD              SPIM2_SPIS2_SPI2_IRQHandler
  123 000000D0 00000000        DCD              RTC2_IRQHandler
  124 000000D4 00000000        DCD              I2S_IRQHandler
  125 000000D8 00000000        DCD              FPU_IRQHandler
  126 000000DC 00000000        DCD              0           ; Reserved
  127 000000E0 00000000        DCD              0           ; Reserved
  128 000000E4 00000000        DCD              0           ; Reserved
  129 000000E8 00000000        DCD              0           ; Reserved
  130 000000EC 00000000        DCD              0           ; Reserved
  131 000000F0 00000000        DCD              0           ; Reserved
  132 000000F4 00000000        DCD              0           ; Reserved
  133 000000F8 00000000        DCD              0           ; Reserved
  134 000000FC 00000000        DCD              0           ; Reserved
  135 00000100 00000000        DCD              0           ; Reserved
  136 00000104 00000000        DCD              0           ; Reserved
  137 00000108 00000000        DCD              0           ; Reserved
  138 0000010C 00000000        DCD              0           ; Reserved
  139 00000110 00000000        DCD              0           ; Reserved
  140 00000114 00000000        DCD              0           ; Reserved
  141 00000118 00000000        DCD              0           ; Reserved
  142 0000011C 00000000        DCD              0           ; Reserved
  143 00000120 00000000        DCD              0           ; Reserved
  144 00000124 00000000        DCD              0           ; Reserved
  145 00000128 00000000        DCD              0           ; Reserved
  146 0000012C 00000000        DCD              0           ; Reserved
  147 00000130 00000000        DCD              0           ; Reserved
  148 00000134 00000000        DCD              0           ; Reserved
  149 00000138 00000000        DCD              0           ; Reserved
  150 0000013C 00000000        DCD              0           ; Reserved
  151 00000140 00000000        DCD              0           ; Reserved
  152 00000144 00000000        DCD              0           ; Reserved
  153 00000148 00000000        DCD              0           ; Reserved
  154 0000014C 00000000        DCD              0           ; Reserved
  155 00000150 00000000        DCD              0           ; Reserved
  156 00000154 00000000        DCD              0           ; Reserved
  157 00000158 00000000        DCD              0           ; Reserved
  158 0000015C 00000000        DCD              0           ; Reserved
  159 00000160 00000000        DCD              0           ; Reserved
  160 00000164 00000000        DCD              0           ; Reserved
  161 00000168 00000000        DCD              0           ; Reserved
  162 0000016C 00000000        DCD              0           ; Reserved
  163 00000170 00000000        DCD              0           ; Reserved
  164 00000174 00000000        DCD              0           ; Reserved
  165 00000178 00000000        DCD              0           ; Reserved
  166 0000017C 00000000        DCD              0           ; Reserved
  167 00000180 00000000        DCD              0           ; Reserved
  168 00000184 00000000        DCD              0           ; Reserved
  169 00000188 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 4 


  170 0000018C 00000000        DCD              0           ; Reserved
  171 00000190 00000000        DCD              0           ; Reserved
  172 00000194 00000000        DCD              0           ; Reserved
  173 00000198 00000000        DCD              0           ; Reserved
  174 0000019C 00000000        DCD              0           ; Reserved
  175 000001A0 00000000        DCD              0           ; Reserved
  176 000001A4 00000000        DCD              0           ; Reserved
  177 000001A8 00000000        DCD              0           ; Reserved
  178 000001AC 00000000        DCD              0           ; Reserved
  179 000001B0 00000000        DCD              0           ; Reserved
  180 000001B4 00000000        DCD              0           ; Reserved
  181 000001B8 00000000        DCD              0           ; Reserved
  182 000001BC 00000000        DCD              0           ; Reserved
  183 000001C0 00000000        DCD              0           ; Reserved
  184 000001C4 00000000        DCD              0           ; Reserved
  185 000001C8 00000000        DCD              0           ; Reserved
  186 000001CC 00000000        DCD              0           ; Reserved
  187 000001D0 00000000        DCD              0           ; Reserved
  188 000001D4 00000000        DCD              0           ; Reserved
  189 000001D8 00000000        DCD              0           ; Reserved
  190 000001DC 00000000        DCD              0           ; Reserved
  191 000001E0 00000000        DCD              0           ; Reserved
  192 000001E4 00000000        DCD              0           ; Reserved
  193 000001E8 00000000        DCD              0           ; Reserved
  194 000001EC 00000000        DCD              0           ; Reserved
  195 000001F0 00000000        DCD              0           ; Reserved
  196 000001F4 00000000        DCD              0           ; Reserved
  197 000001F8 00000000        DCD              0           ; Reserved
  198 000001FC 00000000        DCD              0           ; Reserved
  199 00000200         
  200 00000200         __Vectors_End
  201 00000200         
  202 00000200 00000200 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  203 00000200         
  204 00000200                 AREA             |.text|, CODE, READONLY
  205 00000000         
  206 00000000         ; Reset Handler
  207 00000000         
  208 00000000         
  209 00000000         Reset_Handler
                               PROC
  210 00000000                 EXPORT           Reset_Handler             [WEAK
]
  211 00000000                 IMPORT           SystemInit
  212 00000000                 IMPORT           __main
  213 00000000         
  214 00000000         
  215 00000000 4806            LDR              R0, =SystemInit
  216 00000002 4780            BLX              R0
  217 00000004 4806            LDR              R0, =__main
  218 00000006 4700            BX               R0
  219 00000008                 ENDP
  220 00000008         
  221 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  222 00000008         
  223 00000008         NMI_Handler



ARM Macro Assembler    Page 5 


                               PROC
  224 00000008                 EXPORT           NMI_Handler               [WEAK
]
  225 00000008 E7FE            B                .
  226 0000000A                 ENDP
  228 0000000A         HardFault_Handler
                               PROC
  229 0000000A                 EXPORT           HardFault_Handler         [WEAK
]
  230 0000000A E7FE            B                .
  231 0000000C                 ENDP
  233 0000000C         MemoryManagement_Handler
                               PROC
  234 0000000C                 EXPORT           MemoryManagement_Handler  [WEAK
]
  235 0000000C E7FE            B                .
  236 0000000E                 ENDP
  238 0000000E         BusFault_Handler
                               PROC
  239 0000000E                 EXPORT           BusFault_Handler          [WEAK
]
  240 0000000E E7FE            B                .
  241 00000010                 ENDP
  243 00000010         UsageFault_Handler
                               PROC
  244 00000010                 EXPORT           UsageFault_Handler        [WEAK
]
  245 00000010 E7FE            B                .
  246 00000012                 ENDP
  247 00000012         SVC_Handler
                               PROC
  248 00000012                 EXPORT           SVC_Handler               [WEAK
]
  249 00000012 E7FE            B                .
  250 00000014                 ENDP
  252 00000014         DebugMon_Handler
                               PROC
  253 00000014                 EXPORT           DebugMon_Handler          [WEAK
]
  254 00000014 E7FE            B                .
  255 00000016                 ENDP
  256 00000016         PendSV_Handler
                               PROC
  257 00000016                 EXPORT           PendSV_Handler            [WEAK
]
  258 00000016 E7FE            B                .
  259 00000018                 ENDP
  260 00000018         SysTick_Handler
                               PROC
  261 00000018                 EXPORT           SysTick_Handler           [WEAK
]
  262 00000018 E7FE            B                .
  263 0000001A                 ENDP
  264 0000001A         
  265 0000001A         Default_Handler
                               PROC
  266 0000001A         
  267 0000001A                 EXPORT           POWER_CLOCK_IRQHandler [WEAK]
  268 0000001A                 EXPORT           RADIO_IRQHandler [WEAK]



ARM Macro Assembler    Page 6 


  269 0000001A                 EXPORT           UARTE0_UART0_IRQHandler [WEAK]
  270 0000001A                 EXPORT           SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TW
I0_IRQHandler [WEAK]
  271 0000001A                 EXPORT           SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TW
I1_IRQHandler [WEAK]
  272 0000001A                 EXPORT           NFCT_IRQHandler [WEAK]
  273 0000001A                 EXPORT           GPIOTE_IRQHandler [WEAK]
  274 0000001A                 EXPORT           SAADC_IRQHandler [WEAK]
  275 0000001A                 EXPORT           TIMER0_IRQHandler [WEAK]
  276 0000001A                 EXPORT           TIMER1_IRQHandler [WEAK]
  277 0000001A                 EXPORT           TIMER2_IRQHandler [WEAK]
  278 0000001A                 EXPORT           RTC0_IRQHandler [WEAK]
  279 0000001A                 EXPORT           TEMP_IRQHandler [WEAK]
  280 0000001A                 EXPORT           RNG_IRQHandler [WEAK]
  281 0000001A                 EXPORT           ECB_IRQHandler [WEAK]
  282 0000001A                 EXPORT           CCM_AAR_IRQHandler [WEAK]
  283 0000001A                 EXPORT           WDT_IRQHandler [WEAK]
  284 0000001A                 EXPORT           RTC1_IRQHandler [WEAK]
  285 0000001A                 EXPORT           QDEC_IRQHandler [WEAK]
  286 0000001A                 EXPORT           COMP_LPCOMP_IRQHandler [WEAK]
  287 0000001A                 EXPORT           SWI0_EGU0_IRQHandler [WEAK]
  288 0000001A                 EXPORT           SWI1_EGU1_IRQHandler [WEAK]
  289 0000001A                 EXPORT           SWI2_EGU2_IRQHandler [WEAK]
  290 0000001A                 EXPORT           SWI3_EGU3_IRQHandler [WEAK]
  291 0000001A                 EXPORT           SWI4_EGU4_IRQHandler [WEAK]
  292 0000001A                 EXPORT           SWI5_EGU5_IRQHandler [WEAK]
  293 0000001A                 EXPORT           TIMER3_IRQHandler [WEAK]
  294 0000001A                 EXPORT           TIMER4_IRQHandler [WEAK]
  295 0000001A                 EXPORT           PWM0_IRQHandler [WEAK]
  296 0000001A                 EXPORT           PDM_IRQHandler [WEAK]
  297 0000001A                 EXPORT           MWU_IRQHandler [WEAK]
  298 0000001A                 EXPORT           PWM1_IRQHandler [WEAK]
  299 0000001A                 EXPORT           PWM2_IRQHandler [WEAK]
  300 0000001A                 EXPORT           SPIM2_SPIS2_SPI2_IRQHandler [WE
AK]
  301 0000001A                 EXPORT           RTC2_IRQHandler [WEAK]
  302 0000001A                 EXPORT           I2S_IRQHandler [WEAK]
  303 0000001A                 EXPORT           FPU_IRQHandler [WEAK]
  304 0000001A         POWER_CLOCK_IRQHandler
  305 0000001A         RADIO_IRQHandler
  306 0000001A         UARTE0_UART0_IRQHandler
  307 0000001A         SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
  308 0000001A         SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler
  309 0000001A         NFCT_IRQHandler
  310 0000001A         GPIOTE_IRQHandler
  311 0000001A         SAADC_IRQHandler
  312 0000001A         TIMER0_IRQHandler
  313 0000001A         TIMER1_IRQHandler
  314 0000001A         TIMER2_IRQHandler
  315 0000001A         RTC0_IRQHandler
  316 0000001A         TEMP_IRQHandler
  317 0000001A         RNG_IRQHandler
  318 0000001A         ECB_IRQHandler
  319 0000001A         CCM_AAR_IRQHandler
  320 0000001A         WDT_IRQHandler
  321 0000001A         RTC1_IRQHandler
  322 0000001A         QDEC_IRQHandler
  323 0000001A         COMP_LPCOMP_IRQHandler
  324 0000001A         SWI0_EGU0_IRQHandler



ARM Macro Assembler    Page 7 


  325 0000001A         SWI1_EGU1_IRQHandler
  326 0000001A         SWI2_EGU2_IRQHandler
  327 0000001A         SWI3_EGU3_IRQHandler
  328 0000001A         SWI4_EGU4_IRQHandler
  329 0000001A         SWI5_EGU5_IRQHandler
  330 0000001A         TIMER3_IRQHandler
  331 0000001A         TIMER4_IRQHandler
  332 0000001A         PWM0_IRQHandler
  333 0000001A         PDM_IRQHandler
  334 0000001A         MWU_IRQHandler
  335 0000001A         PWM1_IRQHandler
  336 0000001A         PWM2_IRQHandler
  337 0000001A         SPIM2_SPIS2_SPI2_IRQHandler
  338 0000001A         RTC2_IRQHandler
  339 0000001A         I2S_IRQHandler
  340 0000001A         FPU_IRQHandler
  341 0000001A E7FE            B                .
  342 0000001C                 ENDP
  343 0000001C                 ALIGN
  344 0000001C         
  345 0000001C         ; User Initial Stack & Heap
  346 0000001C         
  347 0000001C                 IF               :DEF:__MICROLIB
  348 0000001C         
  349 0000001C                 EXPORT           __initial_sp
  350 0000001C                 EXPORT           __heap_base
  351 0000001C                 EXPORT           __heap_limit
  352 0000001C         
  353 0000001C                 ELSE
  369                          ENDIF
  370 0000001C         
  371 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\_build\arm_startup_nrf52.d -o.\_build\arm_startup_nrf52.o 
-I..\..\config -I..\nRF5_SDK_17.0.2_d674dde\components\ble\common -I..\nRF5_SDK
_17.0.2_d674dde\components\boards -I..\nRF5_SDK_17.0.2_d674dde\components\libra
ries\atomic -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\atomic_fifo -I..\
nRF5_SDK_17.0.2_d674dde\components\libraries\balloc -I..\nRF5_SDK_17.0.2_d674dd
e\components\libraries\bootloader -I..\nRF5_SDK_17.0.2_d674dde\components\libra
ries\bootloader\ble_dfu -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\bootl
oader\dfu -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crc32 -I..\nRF5_SDK
_17.0.2_d674dde\components\libraries\crypto -I..\nRF5_SDK_17.0.2_d674dde\compon
ents\libraries\crypto\backend\cc310 -I..\nRF5_SDK_17.0.2_d674dde\components\lib
raries\crypto\backend\cc310_bl -I..\nRF5_SDK_17.0.2_d674dde\components\librarie
s\crypto\backend\cifra -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crypto
\backend\mbedtls -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crypto\backe
nd\micro_ecc -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crypto\backend\n
rf_hw -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crypto\backend\nrf_sw -
I..\nRF5_SDK_17.0.2_d674dde\components\libraries\crypto\backend\oberon -I..\nRF
5_SDK_17.0.2_d674dde\components\libraries\crypto\backend\optiga -I..\nRF5_SDK_1
7.0.2_d674dde\components\libraries\delay -I..\nRF5_SDK_17.0.2_d674dde\component
s\libraries\experimental_section_vars -I..\nRF5_SDK_17.0.2_d674dde\components\l
ibraries\fstorage -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\log -I..\nR
F5_SDK_17.0.2_d674dde\components\libraries\log\src -I..\nRF5_SDK_17.0.2_d674dde
\components\libraries\mem_manager -I..\nRF5_SDK_17.0.2_d674dde\components\libra
ries\memobj -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\queue -I..\nRF5_S
DK_17.0.2_d674dde\components\libraries\ringbuf -I..\nRF5_SDK_17.0.2_d674dde\com



ARM Macro Assembler    Page 8 


ponents\libraries\scheduler -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\s
ha256 -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\stack_info -I..\nRF5_SD
K_17.0.2_d674dde\components\libraries\strerror -I..\nRF5_SDK_17.0.2_d674dde\com
ponents\libraries\svc -I..\nRF5_SDK_17.0.2_d674dde\components\libraries\util -I
..\nRF5_SDK_17.0.2_d674dde\components\softdevice\common -I..\nRF5_SDK_17.0.2_d6
74dde\components\softdevice\s132\headers -I..\nRF5_SDK_17.0.2_d674dde\component
s\softdevice\s132\headers\nrf52 -I..\.. -I..\nRF5_SDK_17.0.2_d674dde\external\f
printf -I..\nRF5_SDK_17.0.2_d674dde\external\micro-ecc\micro-ecc -I..\nRF5_SDK_
17.0.2_d674dde\external\nano-pb -I..\nRF5_SDK_17.0.2_d674dde\external\nrf_obero
n -I..\nRF5_SDK_17.0.2_d674dde\external\nrf_oberon\include -I..\nRF5_SDK_17.0.2
_d674dde\integration\nrfx -I..\nRF5_SDK_17.0.2_d674dde\modules\nrfx -I..\nRF5_S
DK_17.0.2_d674dde\modules\nrfx\hal -I..\config -I.\RTE\_ysj-20-fw-boot -IC:\Use
rs\zhangqi\AppData\Local\Arm\Packs\ARM\CMSIS\5.6.0\CMSIS\Core\Include -IC:\User
s\zhangqi\AppData\Local\Arm\Packs\NordicSemiconductor\nRF_DeviceFamilyPack\8.35
.0\Device\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSIO
N SETA 534" --predefine="_RTE_ SETA 1" --predefine="NRF52832_XXAA SETA 1" --pre
define="_RTE_ SETA 1" --predefine="BLE_STACK_SUPPORT_REQD SETA 1" --predefine="
BOARD_PCA10040 SETA 1" --predefine="CONFIG_GPIO_AS_PINRESET SETA 1" --predefine
="FLOAT_ABI_HARD SETA 1" --predefine="NRF52 SETA 1" --predefine="NRF52832_XXAA 
SETA 1" --predefine="NRF52_PAN_74 SETA 1" --predefine="NRF_DFU_SETTINGS_VERSION
 SETA 2" --predefine="NRF_DFU_SVCI_ENABLED SETA 1" --predefine="NRF_SD_BLE_API_
VERSION SETA 7" --predefine="S132 SETA 1" --predefine="SOFTDEVICE_PRESENT SETA 
1" --predefine="SVC_INTERFACE_CALL_AS_NORMAL_FUNCTION SETA 1" --predefine="__HE
AP_SIZE SETA 0" --predefine="uECC_ENABLE_VLI_API SETA 0" --predefine="uECC_OPTI
MIZATION_LEVEL SETA 3" --predefine="uECC_SQUARE_FUNC SETA 0" --predefine="uECC_
SUPPORT_COMPRESSED_POINT SETA 0" --predefine="uECC_VLI_NATIVE_LITTLE_ENDIAN SET
A 1" --cpreproc_opts=-DBLE_STACK_SUPPORT_REQD,-DBOARD_PCA10040,-DCONFIG_GPIO_AS
_PINRESET,-DFLOAT_ABI_HARD,-DNRF52,-DNRF52832_XXAA,-DNRF52_PAN_74,-DNRF_DFU_SET
TINGS_VERSION=2,-DNRF_DFU_SVCI_ENABLED,-DNRF_SD_BLE_API_VERSION=7,-DS132,-DSOFT
DEVICE_PRESENT,-DSVC_INTERFACE_CALL_AS_NORMAL_FUNCTION,-D__HEAP_SIZE=0,-DuECC_E
NABLE_VLI_API=0,-DuECC_OPTIMIZATION_LEVEL=3,-DuECC_SQUARE_FUNC=0,-DuECC_SUPPORT
_COMPRESSED_POINT=0,-DuECC_VLI_NATIVE_LITTLE_ENDIAN=1 --list=.\_build\arm_start
up_nrf52.lst RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 42 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 43 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00001000

Symbol: __initial_sp
   Definitions
      At line 44 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 69 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 349 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 54 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 56 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 55 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 350 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: __heap_base used once
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 57 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 351 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 64 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 69 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 65 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 202 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

__Vectors_End 00000200

Symbol: __Vectors_End
   Definitions
      At line 200 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 66 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 202 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 204 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: .text unused
BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 238 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 74 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 239 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

CCM_AAR_IRQHandler 0000001A

Symbol: CCM_AAR_IRQHandler
   Definitions
      At line 319 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 102 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 282 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

COMP_LPCOMP_IRQHandler 0000001A

Symbol: COMP_LPCOMP_IRQHandler
   Definitions
      At line 323 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 106 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 286 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 252 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 81 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 253 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 265 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      None
Comment: Default_Handler unused
ECB_IRQHandler 0000001A

Symbol: ECB_IRQHandler
   Definitions
      At line 318 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 101 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 281 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 340 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 125 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 303 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

GPIOTE_IRQHandler 0000001A

Symbol: GPIOTE_IRQHandler
   Definitions
      At line 310 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 93 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 273 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 228 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 72 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 229 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

I2S_IRQHandler 0000001A

Symbol: I2S_IRQHandler
   Definitions
      At line 339 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 124 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 302 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

MWU_IRQHandler 0000001A

Symbol: MWU_IRQHandler
   Definitions
      At line 334 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 119 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 297 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

MemoryManagement_Handler 0000000C

Symbol: MemoryManagement_Handler
   Definitions
      At line 233 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 73 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 234 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

NFCT_IRQHandler 0000001A

Symbol: NFCT_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 309 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 92 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 272 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 223 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 71 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 224 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

PDM_IRQHandler 0000001A

Symbol: PDM_IRQHandler
   Definitions
      At line 333 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 116 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 296 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

POWER_CLOCK_IRQHandler 0000001A

Symbol: POWER_CLOCK_IRQHandler
   Definitions
      At line 304 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 87 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 267 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

PWM0_IRQHandler 0000001A

Symbol: PWM0_IRQHandler
   Definitions
      At line 332 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 115 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 295 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

PWM1_IRQHandler 0000001A

Symbol: PWM1_IRQHandler
   Definitions
      At line 335 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 120 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 298 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

PWM2_IRQHandler 0000001A

Symbol: PWM2_IRQHandler
   Definitions
      At line 336 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 121 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 299 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 256 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 83 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 257 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

QDEC_IRQHandler 0000001A

Symbol: QDEC_IRQHandler
   Definitions
      At line 322 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 105 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 285 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

RADIO_IRQHandler 0000001A

Symbol: RADIO_IRQHandler
   Definitions
      At line 305 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 88 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 268 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

RNG_IRQHandler 0000001A

Symbol: RNG_IRQHandler
   Definitions
      At line 317 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 100 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 280 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

RTC0_IRQHandler 0000001A

Symbol: RTC0_IRQHandler
   Definitions
      At line 315 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 98 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 278 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

RTC1_IRQHandler 0000001A

Symbol: RTC1_IRQHandler
   Definitions
      At line 321 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 104 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 284 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

RTC2_IRQHandler 0000001A

Symbol: RTC2_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 338 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 123 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 301 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 209 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 70 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 210 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SAADC_IRQHandler 0000001A

Symbol: SAADC_IRQHandler
   Definitions
      At line 311 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 94 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 274 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler 0000001A

Symbol: SPIM0_SPIS0_TWIM0_TWIS0_SPI0_TWI0_IRQHandler
   Definitions
      At line 307 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 90 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 270 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler 0000001A

Symbol: SPIM1_SPIS1_TWIM1_TWIS1_SPI1_TWI1_IRQHandler
   Definitions
      At line 308 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 91 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 271 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SPIM2_SPIS2_SPI2_IRQHandler 0000001A

Symbol: SPIM2_SPIS2_SPI2_IRQHandler
   Definitions
      At line 337 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 122 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 300 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 247 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 80 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 248 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

SWI0_EGU0_IRQHandler 0000001A

Symbol: SWI0_EGU0_IRQHandler
   Definitions
      At line 324 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 107 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 287 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SWI1_EGU1_IRQHandler 0000001A

Symbol: SWI1_EGU1_IRQHandler
   Definitions
      At line 325 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 108 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 288 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SWI2_EGU2_IRQHandler 0000001A

Symbol: SWI2_EGU2_IRQHandler
   Definitions
      At line 326 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 109 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 289 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SWI3_EGU3_IRQHandler 0000001A

Symbol: SWI3_EGU3_IRQHandler
   Definitions
      At line 327 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 110 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 290 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SWI4_EGU4_IRQHandler 0000001A

Symbol: SWI4_EGU4_IRQHandler
   Definitions
      At line 328 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 111 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 291 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SWI5_EGU5_IRQHandler 0000001A

Symbol: SWI5_EGU5_IRQHandler
   Definitions
      At line 329 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 112 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 292 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 260 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 84 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 261 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TEMP_IRQHandler 0000001A

Symbol: TEMP_IRQHandler
   Definitions
      At line 316 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 99 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 279 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TIMER0_IRQHandler 0000001A

Symbol: TIMER0_IRQHandler
   Definitions
      At line 312 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 95 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 275 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TIMER1_IRQHandler 0000001A

Symbol: TIMER1_IRQHandler
   Definitions
      At line 313 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 96 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 276 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TIMER2_IRQHandler 0000001A

Symbol: TIMER2_IRQHandler
   Definitions
      At line 314 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 97 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 277 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TIMER3_IRQHandler 0000001A

Symbol: TIMER3_IRQHandler
   Definitions
      At line 330 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 113 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 293 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

TIMER4_IRQHandler 0000001A

Symbol: TIMER4_IRQHandler
   Definitions
      At line 331 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 114 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 294 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

UARTE0_UART0_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: UARTE0_UART0_IRQHandler
   Definitions
      At line 306 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 89 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 269 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 243 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 75 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 244 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

WDT_IRQHandler 0000001A

Symbol: WDT_IRQHandler
   Definitions
      At line 320 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 103 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
      At line 283 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s

49 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000000

Symbol: Heap_Size
   Definitions
      At line 49 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 56 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: Heap_Size used once
Stack_Align 00000003

Symbol: Stack_Align
   Definitions
      At line 39 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 42 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: Stack_Align used once
Stack_Size 00001000

Symbol: Stack_Size
   Definitions
      At line 33 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 43 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: Stack_Size used once
__Vectors_Size 00000200

Symbol: __Vectors_Size
   Definitions
      At line 202 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 67 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: __Vectors_Size used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 211 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 215 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 212 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
   Uses
      At line 217 in file RTE\Device\nRF52832_xxAA\arm_startup_nrf52.s
Comment: __main used once
2 symbols
430 symbols in table
