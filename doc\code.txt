
1、断开BLE连接
err_code = sd_ble_gap_disconnect(m_conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            if (err_code != NRF_ERROR_INVALID_STATE)
            {
                APP_ERROR_CHECK(err_code);
            }




2、休眠
	// Prepare wakeup buttons.
    err_code = bsp_btn_ble_sleep_mode_prepare();
    APP_ERROR_CHECK(err_code);

    // Go to system-off mode (this function will not return; wakeup will cause a reset).
    err_code = sd_power_system_off();
    APP_ERROR_CHECK(err_code);
	
3、定时器
static void battery_level_meas_timeout_handler(TimerHandle_t xTimer)
{
    UNUSED_PARAMETER(xTimer);
    
}


/**@brief Function for the Timer initialization.
 *
 * @details Initializes the timer module. This creates and starts application timers.
 */
static void timers_init(void)
{
    // Initialize timer module.
    ret_code_t err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);

    // Create timers.
    m_battery_timer = xTimerCreate("BATT", 1000, pdTRUE, NULL, battery_level_meas_timeout_handler);

    /* Error checking */
    if (NULL == m_battery_timer)
    {
        APP_ERROR_HANDLER(NRF_ERROR_NO_MEM);
    }
}

/**@brief   Function for starting application timers.
 * @details Timers are run after the scheduler has started.
 */
static void application_timers_start(void)
{
    // Start application timers.
    if (pdPASS != xTimerStart(m_battery_timer, OSTIMER_WAIT_FOR_QUEUE))
    {
        APP_ERROR_HANDLER(NRF_ERROR_NO_MEM);
    }
}

4、扫描过程中手动连接
1)设置init_scan.connect_if_match = false;


2)扫描事件回调中连接对端
scan_evt_handler()中的case NRF_BLE_SCAN_EVT_FILTER_MATCH中，
获取对端地址
ble_gap_addr_t peer_addr;
memcpy(&peer_addr, &p_scan_evt->params.filter_match.p_adv_report->peer_addr, sizeof(peer_addr));
连接对端
ble_gap_scan_params_t m_ble_gap_scan_params = 
{
    // Set the default parameters.
    .active        = 1,
    .interval      = NRF_BLE_SCAN_SCAN_INTERVAL,
    .window        = NRF_BLE_SCAN_SCAN_WINDOW,
    .timeout       = NRF_BLE_SCAN_SCAN_DURATION,
    .filter_policy = BLE_GAP_SCAN_FP_ACCEPT_ALL,
    .scan_phys     = BLE_GAP_PHY_1MBPS
};
ble_gap_conn_params_t m_ble_gap_conn_params = 
{
    .conn_sup_timeout = (uint16_t)MSEC_TO_UNITS(NRF_BLE_SCAN_SUPERVISION_TIMEOUT, UNIT_10_MS),
    .min_conn_interval = (uint16_t)MSEC_TO_UNITS(NRF_BLE_SCAN_MIN_CONNECTION_INTERVAL, UNIT_1_25_MS),
    .max_conn_interval = (uint16_t)MSEC_TO_UNITS(NRF_BLE_SCAN_MAX_CONNECTION_INTERVAL, UNIT_1_25_MS),
    .slave_latency = (uint16_t)NRF_BLE_SCAN_SLAVE_LATENCY
};
sd_ble_gap_connect(&peer_addr, &m_ble_gap_scan_params, &m_ble_gap_conn_params, APP_BLE_CONN_CFG_TAG);

3)在ble_evt_handler()中的case BLE_GAP_EVT_CONNECTED会自动重新扫描
	
	
	