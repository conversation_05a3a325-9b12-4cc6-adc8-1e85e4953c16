{"configurations": [{"name": "(gdb) 启动", "type": "cppdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/path/to/gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "(Windows) 启动", "type": "cppvsdbg", "request": "launch", "program": "输入程序名称，例如 ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "externalTerminal"}], "version": "2.0.0"}